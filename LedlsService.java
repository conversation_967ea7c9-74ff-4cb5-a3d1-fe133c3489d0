package com.listenvision;

import com.dpos.gap.modules.gate.service.impl.GateIoTriggerServiceImpl;
import com.dpos.model.gate.LedLsParamVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;

/**
 * 类描述：
 * <AUTHOR>
 * @date 2025/6/9 15:57
 */
@Service
public class LedlsService {
    private static final Logger log = LoggerFactory.getLogger(GateIoTriggerServiceImpl.class);

    @PostConstruct
    public void init()
    {
    	try {
			//Bx6GEnv.initial(30000);
            String OS = System.getProperty("os.name").toLowerCase();
            if (OS.contains("win")){
                //led.InitLedType(1);//卡型号    0 T/A/U/XC/W     1 E     2 X     3 C
            }else if (OS.contains("nix") || OS.contains("nux") || OS.contains("aix")) {
                led.InitLedType(1);//卡型号    0 T/A/U/XC/W     1 E     2 X     3 C
            }
		} catch (Exception e) {
			e.printStackTrace();
		}
    }
    /**
     * 灵信发送
     * @param ledLsParam
     * @param ip
     * @param sendContent
     */
    public void sendLedLs(LedLsParamVO ledLsParam, String ip, String sendContent){
        log.info("LED发送:{},{}",ip,sendContent);
        try {
            led.InitLedRgb(0); //模组的RGB顺序,仅C卡有效,其他卡固定为0. C卡时, 0:  R->G->B 1: G->R->B 2:R->B->G 3:B->R->G 4:B->G->R 5:G->B->R
            //添加一个多行文本到图文区
            //SaveType		节目保存位置，默认为0保存为flash节目，3保存为ram节目。注：flash节目掉电不清除，ram节目掉电清除。应用场景需要实时刷新的，建议保持为ram节目
            //目前仅C卡程序才支持切换,  其他卡默认出货为flash程序,如果需要RAM程序请联系业务或者在官网下载,然后使用Led Player对卡进行升级
            long hProgram = led.CreateProgram(ledLsParam.getLedWidth(), ledLsParam.getLedHeight(), ledLsParam.getColorType(), ledLsParam.getGrayLevel(), 0);
            //	hProgram		节目对象句柄
            //	ProgramNo		节目号（取值范围0-255)（从0开始）
            //	ProgramTime		节目播放时长 0.节目播放时长  非0.指定播放时长
            //	LoopCount		循环播放次数
            led.AddProgram(hProgram, 0, 0, 1);
            // hProgram			节目对象句柄
            //	ProgramNo			节目号（取值范围0-255)（从0开始）
            //	AreaNo				区域号（取值范围1-255）
            //	 l					区域左上角横坐标
            //	 t					区域左上角纵坐标
            //	 w					区域宽度
            //	 h					区域高度
            //	 nLayout				区域层号,0.前景区（默认） 1.背景区  注：除C系列，其它默认为1
            //	 *	返回值
            //	 0					成功
            //	 非0					失败
           int addResult= led.AddImageTextArea(hProgram, 0, 1, 0, 0, ledLsParam.getLedWidth(), ledLsParam.getLedHeight(), 1);
            //             *	参数说明
            // hProgram				节目对象句柄
            // ProgramNo			节目号（取值范围0-255)（从0开始）
            // AreaNo				区域号（取值范围1-255）
            // AddType				添加的类型  0.为字符串  1.文件（只支持txt）
            // AddStr				AddType为0则为字符串数据,AddType为1则为文件路径
            // FontName				字库文件路径
            // FontSize				字体大小
            // FontColor			字体颜色  格式BBGGRR 0xff 红色  0xff00 绿色  0xffff黄色
            // FontBold				是否加粗 0不加粗 1加粗
            // FontItalic			是否是斜体  0 不斜 1斜
            // FontUnderline		是否下划线 0不加下划线 1加下划线
            // DelayTime			停留时间	(1-65535)  秒    注：当入场特技为连续左移、连续右移、连续上移、连续下移时，此参数无效
            // nAlignment			左右居中对齐方式 0.左对齐  1.水平居中  2.右对齐  （注意：只对字符串和txt文件有效）
            // IsVCenter			是否垂直居中  0.置顶（默认） 1.垂直居中
            // 返回值
            // 0					成功
            // 非0					失败
            led.AddMultiLineTextToImageTextArea(hProgram, 0, 1, 0, sendContent,
                    ledLsParam.getFontName(), ledLsParam.getFontSize(), ledLsParam.getFontColor(), ledLsParam.getFontBold(), ledLsParam.getFontItalic(),
                     ledLsParam.getFontUnderline(), ledLsParam.getInStyle(), ledLsParam.getNspeed(), ledLsParam.getDelayTime(), ledLsParam.getAlignment(), ledLsParam.getIsVcenter());
            int errCode = led.NetWorkSend(ip, hProgram);
            led.DeleteProgram(hProgram);

        }catch (Exception e){
            log.error("LED 灵信发送失败:IP:{},发送内容:{},错误：{}",ip,sendContent,e.toString());
//            String OS = System.getProperty("os.name").toLowerCase();
//            if (OS.contains("win")){
//                //led.InitLedType(1);//卡型号    0 T/A/U/XC/W     1 E     2 X     3 C
//            }else if (OS.contains("nix") || OS.contains("nux") || OS.contains("aix")) {
//                led.InitLedType(1);//卡型号    0 T/A/U/XC/W     1 E     2 X     3 C
//            }
        }
    }
}